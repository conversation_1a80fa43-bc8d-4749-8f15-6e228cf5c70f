﻿import { useQuery } from " @tanstack/react-query\;
import { useAuth } from \@/context/auth-context\;
import { apiRequest } from \@/lib/queryClient\;

import {
 Dialog,
 DialogContentRightSlide,
 DialogHeader,
 DialogTitle,
} from \@/components/ui/dialog-right-slide\;
import { Badge } from \@/components/ui/badge\;
import { Separator } from \@/components/ui/separator\;
import { Skeleton } from \@/components/ui/skeleton\;
import { ArrowRightLeft, Package, Calendar, User, FileText, MapPin } from \lucide-react\;

interface ViewStockTransferModalProps {
 open: boolean;
 onOpenChange: (open: boolean) => void;
 transferId: number | null;
}

export function ViewStockTransferModal({ open, onOpenChange, transferId }: ViewStockTransferModalProps) {
 const { token } = useAuth();

 return (
 <Dialog open={open} onOpenChange={onOpenChange}>
 <DialogContentRightSlide className=\max-w-2xl\>
 <DialogHeader>
 <DialogTitle className=\flex items-center gap-2\>
 <ArrowRightLeft className=\h-5 w-5\ />
 Stock Transfer Details
 </DialogTitle>
 </DialogHeader>
 <div className=\p-4\>
 <p>Transfer ID: {transferId}</p>
 <p>This modal will show transfer details.</p>
 </div>
 </DialogContentRightSlide>
 </Dialog>
 );
}
